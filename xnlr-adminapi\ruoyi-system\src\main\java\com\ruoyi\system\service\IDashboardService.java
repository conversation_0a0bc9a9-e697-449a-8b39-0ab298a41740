package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;

/**
 * 首页数据统计服务接口
 * 
 * <AUTHOR>
 */
public interface IDashboardService 
{
    /**
     * 获取首页统计数据
     * 
     * @return 统计数据
     */
    public Map<String, Object> getDashboardStats();

    /**
     * 获取用户增长趋势数据
     * 
     * @return 趋势数据
     */
    public List<Map<String, Object>> getUserTrendData();

    /**
     * 获取订单趋势数据
     * 
     * @return 趋势数据
     */
    public List<Map<String, Object>> getOrderTrendData();

    /**
     * 获取服务类型分布数据
     * 
     * @return 分布数据
     */
    public List<Map<String, Object>> getServiceDistributionData();

    /**
     * 获取最新订单列表
     * 
     * @return 订单列表
     */
    public List<Map<String, Object>> getRecentOrders();

    /**
     * 获取待审核申请列表
     * 
     * @return 申请列表
     */
    public List<Map<String, Object>> getPendingApplies();
}