<template>
  <div
    :class="{ hidden: hidden }"
    class="pagination-container"
    v-if="total > 0"
  >
    <div class="pagination-info">
      <span class="total-info">
        共 <span class="total-number">{{ total }}</span> 条记录， 第
        <span class="current-page">{{ currentPage }}</span> /
        <span class="total-pages">{{ totalPages }}</span> 页
      </span>
    </div>

    <div class="pagination-controls">
      <el-pagination
        :background="background"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :layout="layout"
        :page-sizes="pageSizes"
        :pager-count="pagerCount"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="custom-pagination"
      />
    </div>
  </div>
</template>

<script setup>
import { scrollTo } from "@/utils/scroll-to";

const props = defineProps({
  total: {
    required: true,
    type: Number,
  },
  page: {
    type: Number,
    default: 1,
  },
  limit: {
    type: Number,
    default: 20,
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50];
    },
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7,
  },
  layout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  background: {
    type: Boolean,
    default: true,
  },
  autoScroll: {
    type: Boolean,
    default: true,
  },
  hidden: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits();
const currentPage = computed({
  get() {
    return props.page;
  },
  set(val) {
    emit("update:page", val);
  },
});
const pageSize = computed({
  get() {
    return props.limit;
  },
  set(val) {
    emit("update:limit", val);
  },
});

const totalPages = computed(() => {
  return Math.ceil(props.total / pageSize.value);
});

function handleSizeChange(val) {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1;
  }
  emit("pagination", { page: currentPage.value, limit: val });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}
function handleCurrentChange(val) {
  emit("pagination", { page: val, limit: pageSize.value });
  if (props.autoScroll) {
    scrollTo(0, 800);
  }
}
</script>

<style scoped>
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  margin-top: 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pagination-container.hidden {
  display: none;
}

.pagination-info {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
}

.total-number,
.current-page,
.total-pages {
  font-weight: bold;
  color: #409eff;
  margin: 0 2px;
}

.pagination-controls {
  display: flex;
  align-items: center;
}

/* 自定义分页器样式 */
:deep(.custom-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #606266;
  --el-pagination-border-radius: 6px;
  --el-pagination-button-color: #606266;
  --el-pagination-button-bg-color: #ffffff;
  --el-pagination-button-disabled-color: #c0c4cc;
  --el-pagination-button-disabled-bg-color: #ffffff;
  --el-pagination-hover-color: #409eff;
  --el-pagination-active-color: #409eff;
}

:deep(.custom-pagination .el-pager li) {
  margin: 0 2px;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

:deep(.custom-pagination .el-pager li:hover) {
  color: #409eff;
  border-color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

:deep(.custom-pagination .el-pager li.is-active) {
  color: #ffffff;
  background-color: #409eff;
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

:deep(.custom-pagination .btn-prev),
:deep(.custom-pagination .btn-next) {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

:deep(.custom-pagination .btn-prev:hover),
:deep(.custom-pagination .btn-next:hover) {
  color: #409eff;
  border-color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

:deep(.custom-pagination .el-pagination__jump) {
  margin-left: 16px;
}

:deep(.custom-pagination .el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s ease;
}

:deep(.custom-pagination .el-input__wrapper:hover) {
  border-color: #409eff;
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .pagination-info {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .pagination-info {
    font-size: 12px;
  }

  :deep(.custom-pagination) {
    --el-pagination-font-size: 12px;
  }

  :deep(.custom-pagination .el-pager li) {
    margin: 0 1px;
    min-width: 28px;
    height: 28px;
    line-height: 26px;
  }
}
</style>
