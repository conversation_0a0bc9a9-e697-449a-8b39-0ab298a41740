<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="请输入用户昵称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="微信" prop="wechat">
        <el-input v-model="queryParams.wechat" placeholder="请输入微信" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="角色" prop="userType">
        <el-select v-model="queryParams.userType" placeholder="请选择角色" clearable>
          <el-option v-for="dict in sys_user_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="性别" prop="sex">
        <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable>
          <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="年龄" prop="age">
        <el-input v-model="queryParams.age" placeholder="请输入年龄" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input v-model="queryParams.province" placeholder="请输入省份" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="是否在线" prop="isOnline">
        <el-input v-model="queryParams.isOnline" placeholder="请输入是否在线" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item label="等级" prop="level">
        <el-input v-model="queryParams.level" placeholder="请输入等级" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:MiniUser:add']"
        >新增</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:MiniUser:edit']">查看</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:MiniUser:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:MiniUser:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="MiniUserList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键，用户唯一标识" align="center" prop="userId" />
      <el-table-column label="openid" align="center" prop="openid" /> -->
      <el-table-column label="用户昵称" align="center" prop="nickName" />
      <el-table-column label="头像" align="center" prop="avatar">
        <template #default="scope">
          <img :src="scope.row.avatar" alt="头像" style="width: 50px; height: 50px; border-radius: 50%;" />
        </template>
      </el-table-column>
      <el-table-column label="角色" align="center" prop="userType">
        <template #default="scope">
          <dict-tag :options="sys_user_type" :value="scope.row.userType" />
        </template>
      </el-table-column>
      <el-table-column label="余额" align="center" prop="balance" />
      <el-table-column label="充值总额" align="center" prop="totalAmount" />
      <el-table-column label="微信" align="center" prop="wechat" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="性别" align="center" prop="sex">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.sex" />
        </template>
      </el-table-column>
      <el-table-column label="年龄" align="center" prop="age" />
      <el-table-column label="省份" align="center" prop="province" />
      <el-table-column label="标签 " align="center" prop="tags" />
      <!-- <el-table-column label="是否在线" align="center" prop="isOnline" /> -->
      <el-table-column label="起步价格" align="center" prop="basePrice" />
      <el-table-column label="签名" align="center" prop="signature" />
      <el-table-column label="等级" align="center" prop="level" />
      <el-table-column label="是否启用" align="center" prop="enabled">
        <template #default="scope">
          <div>{{ scope.row.userType == 3 ? "上架" : scope.row.enabled == 1 ? "上架" : "下架" }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="语音" align="center" prop="voice" width="500">
        <template #default="scope">
          <audio :src="scope.row.voice" controls></audio>
        </template>
      </el-table-column>
      <el-table-column label="语音时长" align="center" prop="voiceTime" /> -->
      <!-- <el-table-column label="创建时间" align="center" prop="createdAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" align="center" prop="updatedAt" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:MiniUser:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:MiniUser:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改用户管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="MiniUserRef" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="openid" prop="openid">
          <el-input v-model="form.openid" placeholder="请输入openid" />
        </el-form-item> -->
        <el-form-item label="用户昵称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="请输入用户昵称" />
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-input v-model="form.avatar" placeholder="请输入头像" />
        </el-form-item>
        <el-form-item label="余额" prop="balance">
          <el-input v-model="form.balance" placeholder="请输入余额" />
        </el-form-item>
        <el-form-item label="充值总额" prop="totalAmount">
          <el-input v-model="form.totalAmount" placeholder="请输入充值总额" />
        </el-form-item>
        <el-form-item label="微信" prop="wechat">
          <el-input v-model="form.wechat" placeholder="请输入微信" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="角色" prop="userType">
          <el-select v-model="form.userType" placeholder="请选择角色">
            <el-option v-for="dict in sys_user_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="请选择性别">
            <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="form.age" placeholder="请输入年龄" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="form.province" placeholder="请输入省份" />
        </el-form-item>
        <el-form-item label="标签 " prop="tags">
          <el-input v-model="form.tags" placeholder="请输入标签 " />
        </el-form-item>
        <el-form-item label="是否在线" prop="isOnline">
          <el-input v-model="form.isOnline" placeholder="请输入是否在线" />
        </el-form-item>
        <el-form-item label="起步价格" prop="basePrice">
          <el-input v-model="form.basePrice" placeholder="请输入起步价格" />
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input v-model="form.signature" placeholder="请输入签名" />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input v-model="form.level" placeholder="请输入等级" />
        </el-form-item>
        <el-form-item label="语音" prop="voice">
          <audio :src="form.voice" controls></audio>
        </el-form-item>
        <el-form-item label="语音时长" prop="voiceTime">
          <el-input v-model="form.voiceTime" placeholder="请输入语音时长" />
        </el-form-item>
        <el-form-item label="是否上架" prop="enabled">
          <el-select v-model="form.enabled" placeholder="是否上架">
            <el-option v-for="dict in sys_user_enabled" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker clearable v-model="form.createdAt" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker clearable v-model="form.updatedAt" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MiniUser">
import { listMiniUser, getMiniUser, delMiniUser, addMiniUser, updateMiniUser } from "@/api/system/MiniUser";

const { proxy } = getCurrentInstance();
const { sys_user_type, sys_user_sex } = proxy.useDict('sys_user_type', 'sys_user_sex');
const sys_user_enabled = [
  { label: '下架', value: '0' },
  { label: '上架', value: '1' }
];


const MiniUserList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    openid: null,
    nickName: null,
    avatar: null,
    wechat: null,
    phone: null,
    balance: null,
    userType: null,
    createdAt: null,
    updatedAt: null,
    sex: null,
    age: null,
    province: null,
    tags: null,
    isOnline: null,
    basePrice: null,
    signature: null,
    level: null,
    totalAmount: null,
    voice: null,
    voiceTime: null
  },
  rules: {
    openid: [
      { required: true, message: "openid不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询用户管理列表 */
function getList() {
  loading.value = true;
  listMiniUser(queryParams.value).then(response => {
    MiniUserList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    userId: null,
    openid: null,
    nickName: null,
    avatar: null,
    wechat: null,
    phone: null,
    balance: null,
    userType: null,
    createdAt: null,
    updatedAt: null,
    sex: null,
    age: null,
    province: null,
    tags: null,
    isOnline: null,
    basePrice: null,
    signature: null,
    level: null,
    totalAmount: null,
    voice: null,
    voiceTime: null
  };
  proxy.resetForm("MiniUserRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加用户管理";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userId = row.userId || ids.value
  getMiniUser(_userId).then(response => {
    form.value = response.data;
    form.value.enabled = form.value.enabled == '1' ? "上架" : "下架";
    open.value = true;
    title.value = "修改用户";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["MiniUserRef"].validate(valid => {
    if (valid) {
      if (form.value.userId != null) {
        updateMiniUser(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMiniUser(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _userIds = row.userId || ids.value;
  proxy.$modal.confirm('是否确认删除用户管理编号为"' + _userIds + '"的数据项？').then(function () {
    return delMiniUser(_userIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/MiniUser/export', {
    ...queryParams.value
  }, `MiniUser_${new Date().getTime()}.xlsx`)
}

getList();
</script>
