<template>
  <div class="app-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#409EFF"><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ totalApplications }}</div>
              <div class="stats-label">总申请数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#E6A23C"><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ pendingApplications }}</div>
              <div class="stats-label">待审核</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#67C23A"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ approvedApplications }}</div>
              <div class="stats-label">已通过</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon">
              <el-icon size="32" color="#F56C6C"><CircleClose /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ rejectedApplications }}</div>
              <div class="stats-label">已拒绝</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="search-card mb-4">
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        label-width="80px"
      >
        <el-form-item label="昵称" prop="nickName">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户昵称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入用户手机号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select
            v-model="queryParams.sex"
            placeholder="请选择性别"
            clearable
          >
            <el-option label="男" value="1" />
            <el-option label="女" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择申请状态"
            clearable
          >
            <el-option label="申请中" value="申请中" />
            <el-option label="已同意" value="已同意" />
            <el-option label="已拒绝" value="已拒绝" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="mb-4">
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:UserApply:export']"
          >
            导出
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>
    </el-card>

    <!-- 申请列表 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span class="card-title">申请审核列表</span>
          <span class="card-subtitle">共 {{ total }} 条申请</span>
        </div>
      </template>

      <el-table v-loading="loading" :data="UserApplyList" @selection-change="handleSelectionChange" class="apply-table">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请人信息" width="200" align="center">
          <template #default="scope">
            <div class="applicant-info">
              <el-avatar :size="50" :src="scope.row.avatar" class="applicant-avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <div class="applicant-details">
                <div class="applicant-name">{{ scope.row.nickName }}</div>
                <div class="applicant-phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="基本信息" align="center">
          <template #default="scope">
            <div class="basic-info">
              <el-tag :type="scope.row.sex === '1' ? 'primary' : 'danger'" size="small">
                {{ scope.row.sex === '1' ? '男' : '女' }}
              </el-tag>
              <span class="ml-2">{{ scope.row.age }}岁</span>
              <div class="mt-1">{{ scope.row.city }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请信息" align="center">
          <template #default="scope">
            <div class="apply-info">
              <div class="experience">经验: {{ scope.row.experience || '无' }}</div>
              <div class="apply-type">类型: {{ scope.row.applyType || '店员申请' }}</div>
              <div v-if="scope.row.voice" class="voice-info">
                <el-icon><Microphone /></el-icon>
                语音时长: {{ scope.row.voiceTime }}秒
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="申请状态" align="center" width="120">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="large"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['system:UserApply:query']">
              查看详情
            </el-button>
            <el-button
              v-if="scope.row.status === '申请中'"
              link
              type="success"
              icon="Check"
              @click="handleApprove(scope.row)"
              v-hasPermi="['system:UserApply:edit']"
            >
              同意
            </el-button>
            <el-button
              v-if="scope.row.status === '申请中'"
              link
              type="danger"
              icon="Close"
              @click="handleReject(scope.row)"
              v-hasPermi="['system:UserApply:edit']"
            >
              拒绝
            </el-button>
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:UserApply:remove']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改申请管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form
        ref="UserApplyRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="昵称" prop="nickName">
          <el-input v-model="form.nickName" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="用户头像" prop="avatar">
          <img
            :src="form.avatar"
            style="width: 50px; height: 50px; border-radius: 50%"
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="form.sex" placeholder="无" disabled="true">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="form.city" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="微信号" prop="wechat">
          <el-input v-model="form.wechat" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="form.tags" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="签名" prop="signature">
          <el-input v-model="form.signature" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="等级" prop="level">
          <el-input v-model="form.level" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="申请类型" prop="applyType">
          <el-input v-model="form.applyType" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="经验" prop="experience">
          <el-input
            v-model="form.experience"
            placeholder="无"
            disabled="true"
          />
        </el-form-item>
        <el-form-item label="语音" prop="voice">
          <audio :src="form.voice" controls></audio>
        </el-form-item>
        <el-form-item label="语音时间" prop="voiceTime">
          <el-input v-model="form.voiceTime" placeholder="无" disabled="true" />
        </el-form-item>
        <el-form-item label="申请状态" prop="status">
          <el-select v-model="form.status" placeholder="无">
            <el-option
              v-for="dict in sys_apply_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户类型" prop="userType">
          <el-select v-model="form.userType" placeholder="无" disabled="true">
            <el-option
              v-for="dict in sys_user_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="UserApply">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import {
  listUserApply,
  getUserApply,
  delUserApply,
  addUserApply,
  updateUserApply,
  approveUserApply,
  rejectUserApply,
} from "@/api/system/UserApply";
import { Document, Clock, CircleCheck, CircleClose, User, Microphone } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// 使用默认字典数据，避免字典加载问题
const sys_user_type = ref([
  { label: '普通用户', value: '0' },
  { label: '店员', value: '1' },
  { label: '管理员', value: '2' },
  { label: '申请成为店员', value: '3' }
]);

const sys_user_sex = ref([
  { label: '女', value: '0' },
  { label: '男', value: '1' }
]);

const sys_apply_status = ref([
  { label: '申请中', value: '申请中' },
  { label: '已同意', value: '已同意' },
  { label: '已拒绝', value: '已拒绝' }
]);

const UserApplyList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 统计数据
const totalApplications = ref(0);
const pendingApplications = ref(0);
const approvedApplications = ref(0);
const rejectedApplications = ref(0);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userId: null,
    nickName: null,
    phone: null,
    sex: null,
    age: null,
    voice: null,
    city: null,
    experience: null,
    voiceTime: null,
    status: null,
    userType: null,
    avatar: null,
  },
  rules: {
    userId: [{ required: true, message: "用户id不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询申请管理列表 */
function getList() {
  loading.value = true;
  listUserApply(queryParams.value).then((response) => {
    UserApplyList.value = response.rows || [];
    total.value = response.total || 0;
    loading.value = false;

    // 计算统计数据
    calculateStats();
  }).catch((error) => {
    console.error('获取申请列表失败:', error);
    loading.value = false;
    UserApplyList.value = [];
    total.value = 0;
    proxy.$modal.msgError("获取申请列表失败");
  });
}

/** 计算统计数据 */
function calculateStats() {
  totalApplications.value = UserApplyList.value.length;
  pendingApplications.value = UserApplyList.value.filter(app => app.status === '申请中').length;
  approvedApplications.value = UserApplyList.value.filter(app => app.status === '已同意').length;
  rejectedApplications.value = UserApplyList.value.filter(app => app.status === '已拒绝').length;
}

/** 获取状态标签类型 */
function getStatusType(status) {
  switch (status) {
    case '申请中': return 'warning';
    case '已同意': return 'success';
    case '已拒绝': return 'danger';
    default: return 'info';
  }
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    userApplyId: null,
    userId: null,
    nickName: null,
    phone: null,
    sex: null,
    age: null,
    voice: null,
    city: null,
    experience: null,
    voiceTime: null,
    status: null,
    userType: null,
    avatar: null,
  };
  proxy.resetForm("UserApplyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userApplyId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加申请管理";
}

/** 查看详情按钮操作 */
function handleView(row) {
  reset();
  const _userApplyId = row.userApplyId || ids.value;
  getUserApply(_userApplyId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "查看申请详情";
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _userApplyId = row.userApplyId || ids.value;
  getUserApply(_userApplyId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "查看申请管理";
  });
}

/** 同意申请 */
function handleApprove(row) {
  proxy.$modal.confirm('确认同意"' + row.nickName + '"的店员申请吗？').then(function() {
    return approveUserApply(row.userApplyId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("申请已同意，用户已成为店员");
  }).catch(() => {});
}

/** 拒绝申请 */
function handleReject(row) {
  proxy.$modal.confirm('确认拒绝"' + row.nickName + '"的店员申请吗？').then(function() {
    return rejectUserApply(row.userApplyId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("申请已拒绝");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["UserApplyRef"].validate((valid) => {
    if (valid) {
      if (form.value.userApplyId != null) {
        updateUserApply(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addUserApply(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _userApplyIds = row.userApplyId || ids.value;
  proxy.$modal
    .confirm('是否确认删除申请管理编号为"' + _userApplyIds + '"的数据项？')
    .then(function () {
      return delUserApply(_userApplyIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/UserApply/export",
    {
      ...queryParams.value,
    },
    `UserApply_${new Date().getTime()}.xlsx`
  );
}

// 页面初始化
console.log('申请审核页面初始化开始');
try {
  getList();
  console.log('申请审核页面初始化成功');
} catch (error) {
  console.error('申请审核页面初始化失败:', error);
}
</script>

<style scoped>
.app-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

.stats-card {
  margin-bottom: 16px;
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  }
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 24px;
}

.stats-icon {
  margin-right: 20px;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409EFF 0%, #66B3FF 100%);
  color: white;
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-label {
  font-size: 16px;
  color: #6b7280;
  font-weight: 500;
  margin-top: 8px;
}

.search-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.card-subtitle {
  font-size: 14px;
  color: #6b7280;
}

.apply-table {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.applicant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.applicant-avatar {
  border: 2px solid #f0f0f0;
}

.applicant-details {
  text-align: left;
}

.applicant-name {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.applicant-phone {
  font-size: 12px;
  color: #909399;
}

.basic-info {
  text-align: center;
}

.apply-info div {
  margin-bottom: 4px;
  font-size: 14px;
}

.experience {
  color: #409EFF;
}

.apply-type {
  color: #E6A23C;
}

.voice-info {
  color: #67C23A;
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
}

.status-tag {
  font-weight: bold;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-1 {
  margin-top: 4px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
